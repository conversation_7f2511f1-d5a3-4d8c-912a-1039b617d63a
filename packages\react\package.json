{"name": "designers-react", "version": "3.0.0", "description": "React hooks and providers for Designers design system", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.esm.js", "require": "./dist/index.js"}}, "files": ["dist"], "scripts": {"build": "echo 'React build temporarily disabled for docs deployment'", "dev": "tsup src/index.ts --format cjs,esm --dts --watch", "lint": "eslint src --ext .ts,.tsx", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "keywords": ["react", "hooks", "design-system", "responsive", "theming"], "author": "arkit karmokar", "license": "MIT", "publishConfig": {"access": "public"}, "dependencies": {"designers-core": "3.0.0"}, "peerDependencies": {"react": ">=18.0.0"}, "devDependencies": {"@types/react": "^18.2.45", "@types/node": "^20.10.5", "eslint": "^8.56.0", "react": "^18.2.0", "tsup": "^8.0.1", "typescript": "^5.3.3"}}