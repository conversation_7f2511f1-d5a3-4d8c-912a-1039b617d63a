{"name": "designers", "version": "3.1.0", "description": "A complete, lightweight, headless design system for React applications", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.esm.js", "require": "./dist/index.js"}, "./core": {"types": "./dist/core.d.ts", "import": "./dist/core.esm.js", "require": "./dist/core.js"}, "./react": {"types": "./dist/react.d.ts", "import": "./dist/react.esm.js", "require": "./dist/react.js"}, "./animations": {"types": "./dist/animations.d.ts", "import": "./dist/animations.esm.js", "require": "./dist/animations.js"}, "./integrations": {"types": "./dist/integrations.d.ts", "import": "./dist/integrations.esm.js", "require": "./dist/integrations.js"}, "./tailwind-plugin": {"types": "./dist/tailwind-plugin.d.ts", "import": "./dist/tailwind-plugin.esm.js", "require": "./dist/tailwind-plugin.js"}, "./cli": {"types": "./dist/cli.d.ts", "import": "./dist/cli.esm.js", "require": "./dist/cli.js"}}, "bin": {"designers": "./dist/cli.js"}, "files": ["dist", "README.md"], "scripts": {"build": "echo 'Designers build temporarily disabled for docs deployment'", "dev": "tsup src/index.ts --format cjs,esm --dts --watch", "lint": "eslint src --ext .ts,.tsx", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "keywords": ["design-system", "react", "typescript", "headless", "design-tokens", "ui", "components", "tailwind", "animations", "framer-motion", "gsap", "shadcn", "mui", "chakra-ui", "mantine"], "author": "arkit karmokar", "license": "MIT", "publishConfig": {"access": "public"}, "dependencies": {"designers-core": "3.0.0", "designers-cli": "2.0.1", "designers-react": "3.0.0", "designers-animations": "2.0.1", "designers-integrations": "2.0.1", "designers-tailwind-plugin": "2.0.1"}, "peerDependencies": {"react": ">=18.0.0", "tailwindcss": ">=3.0.0"}, "peerDependenciesMeta": {"react": {"optional": false}, "tailwindcss": {"optional": true}}, "devDependencies": {"@types/node": "^20.10.5", "@types/react": "^18.2.45", "eslint": "^8.56.0", "react": "^18.2.0", "tailwindcss": "^3.4.0", "tsup": "^8.0.1", "typescript": "^5.3.3"}}