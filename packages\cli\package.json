{"name": "designers-cli", "version": "2.0.1", "description": "Command-line interface for Designers design system", "main": "dist/index.js", "types": "dist/index.d.ts", "bin": {"designers": "dist/cli.js"}, "files": ["dist", "templates"], "scripts": {"build": "echo 'CLI build temporarily disabled for docs deployment'", "dev": "tsup --watch", "lint": "eslint src --ext .ts", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "keywords": ["cli", "design-system", "code-generation", "scaffolding"], "author": "arkit karmokar", "license": "MIT", "publishConfig": {"access": "public"}, "dependencies": {"designers-core": "3.0.0", "commander": "^11.1.0", "inquirer": "^9.2.12", "chalk": "^5.3.0", "ora": "^7.0.1", "fs-extra": "^11.2.0", "handlebars": "^4.7.8", "prettier": "^3.1.1"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.7", "@types/node": "^20.10.5", "eslint": "^8.56.0", "tsup": "^8.0.1", "typescript": "^5.3.3"}}