{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "pipeline": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", ".next/**", "!.next/cache/**"]}, "designers-docs#build": {"dependsOn": ["designers-core#build"], "outputs": [".next/**", "!.next/cache/**"]}, "designers-cli#build": {"dependsOn": ["designers-core#build"], "outputs": ["dist/**"]}, "dev": {"cache": false, "persistent": true}, "lint": {"dependsOn": ["^build"]}, "test": {"dependsOn": ["^build"], "outputs": ["coverage/**"]}, "type-check": {"dependsOn": ["^build"]}, "clean": {"cache": false}}}