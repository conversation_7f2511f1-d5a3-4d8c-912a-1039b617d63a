{"version": 2, "name": "designers-documentation", "framework": "nextjs", "buildCommand": "cd ../.. && npm run build --filter=designers-docs --filter=!designers-cli", "devCommand": "npm run dev", "installCommand": "cd ../.. && npm install", "outputDirectory": ".next", "env": {"NEXT_TELEMETRY_DISABLED": "1", "NODE_ENV": "production"}, "build": {"env": {"NEXT_TELEMETRY_DISABLED": "1"}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/api/(.*)", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}], "redirects": [{"source": "/github", "destination": "https://github.com/Arkit-k/Designers", "permanent": false}, {"source": "/npm", "destination": "https://www.npmjs.com/package/designers", "permanent": false}]}